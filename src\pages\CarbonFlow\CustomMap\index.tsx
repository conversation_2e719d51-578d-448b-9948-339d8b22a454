import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useEffect, useRef } from 'react';
import styles from './custommap.sass';
import { addHeatMap, addThreeModel, addWarning } from './util';

// 设置你的 Mapbox 访问令牌
mapboxgl.accessToken =
  'pk.eyJ1IjoiaGFvMTIzcSIsImEiOiJjazE0ZXdybjMwamRyM21vM2wyMHV2cmRjIn0.JFBk_-UdMEyBJraDh_tTfw';
export default function () {
  const mapContainer = useRef<HTMLDivElement>(null);
  // 使用 ref 存储动画 ID
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    if (!mapContainer.current) return;
    // 初始化地图
    let map = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v11',
      center: [121.4737, 31.2304],
      zoom: 18,
      pitch: 45,
      bearing: 0,
    });
    let tb;
    map.on('load', () => {
      if (!map) return;
      addThreeModel(map, tb);
      addHeatMap(map);
      addWarning(map, animationFrameRef);
    });
    return () => {
      if (map) {
        map.remove();
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);
  return <div className={styles.box} ref={mapContainer} />;
}
